// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_shuffle_param.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fshuffle_5fparam_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fshuffle_5fparam_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fshuffle_5fparam_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto;
namespace abc {
namespace recommend_plt {
namespace strategy {
class BoostParam;
class BoostParamDefaultTypeInternal;
extern BoostParamDefaultTypeInternal _BoostParam_default_instance_;
class ShuffleParam;
class ShuffleParamDefaultTypeInternal;
extern ShuffleParamDefaultTypeInternal _ShuffleParam_default_instance_;
class ShuffleParam_BucketPollingShuffleParam;
class ShuffleParam_BucketPollingShuffleParamDefaultTypeInternal;
extern ShuffleParam_BucketPollingShuffleParamDefaultTypeInternal _ShuffleParam_BucketPollingShuffleParam_default_instance_;
class ShuffleParam_Plan;
class ShuffleParam_PlanDefaultTypeInternal;
extern ShuffleParam_PlanDefaultTypeInternal _ShuffleParam_Plan_default_instance_;
class StrategyParamOld;
class StrategyParamOldDefaultTypeInternal;
extern StrategyParamOldDefaultTypeInternal _StrategyParamOld_default_instance_;
}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> ::abc::recommend_plt::strategy::BoostParam* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::BoostParam>(Arena*);
template<> ::abc::recommend_plt::strategy::ShuffleParam* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::ShuffleParam>(Arena*);
template<> ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam>(Arena*);
template<> ::abc::recommend_plt::strategy::ShuffleParam_Plan* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::ShuffleParam_Plan>(Arena*);
template<> ::abc::recommend_plt::strategy::StrategyParamOld* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::StrategyParamOld>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace abc {
namespace recommend_plt {
namespace strategy {

// ===================================================================

class ShuffleParam_BucketPollingShuffleParam :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam) */ {
 public:
  ShuffleParam_BucketPollingShuffleParam();
  virtual ~ShuffleParam_BucketPollingShuffleParam();

  ShuffleParam_BucketPollingShuffleParam(const ShuffleParam_BucketPollingShuffleParam& from);
  ShuffleParam_BucketPollingShuffleParam(ShuffleParam_BucketPollingShuffleParam&& from) noexcept
    : ShuffleParam_BucketPollingShuffleParam() {
    *this = ::std::move(from);
  }

  inline ShuffleParam_BucketPollingShuffleParam& operator=(const ShuffleParam_BucketPollingShuffleParam& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShuffleParam_BucketPollingShuffleParam& operator=(ShuffleParam_BucketPollingShuffleParam&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ShuffleParam_BucketPollingShuffleParam& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ShuffleParam_BucketPollingShuffleParam* internal_default_instance() {
    return reinterpret_cast<const ShuffleParam_BucketPollingShuffleParam*>(
               &_ShuffleParam_BucketPollingShuffleParam_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(ShuffleParam_BucketPollingShuffleParam* other);
  friend void swap(ShuffleParam_BucketPollingShuffleParam& a, ShuffleParam_BucketPollingShuffleParam& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ShuffleParam_BucketPollingShuffleParam* New() const final {
    return CreateMaybeMessage<ShuffleParam_BucketPollingShuffleParam>(nullptr);
  }

  ShuffleParam_BucketPollingShuffleParam* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ShuffleParam_BucketPollingShuffleParam>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ShuffleParam_BucketPollingShuffleParam& from);
  void MergeFrom(const ShuffleParam_BucketPollingShuffleParam& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShuffleParam_BucketPollingShuffleParam* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto);
    return ::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string filter_dsl = 3;
  void clear_filter_dsl();
  static const int kFilterDslFieldNumber = 3;
  const std::string& filter_dsl() const;
  void set_filter_dsl(const std::string& value);
  void set_filter_dsl(std::string&& value);
  void set_filter_dsl(const char* value);
  void set_filter_dsl(const char* value, size_t size);
  std::string* mutable_filter_dsl();
  std::string* release_filter_dsl();
  void set_allocated_filter_dsl(std::string* filter_dsl);

  // string pass_dsl = 4;
  void clear_pass_dsl();
  static const int kPassDslFieldNumber = 4;
  const std::string& pass_dsl() const;
  void set_pass_dsl(const std::string& value);
  void set_pass_dsl(std::string&& value);
  void set_pass_dsl(const char* value);
  void set_pass_dsl(const char* value, size_t size);
  std::string* mutable_pass_dsl();
  std::string* release_pass_dsl();
  void set_allocated_pass_dsl(std::string* pass_dsl);

  // int32 polling_interval = 1;
  void clear_polling_interval();
  static const int kPollingIntervalFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 polling_interval() const;
  void set_polling_interval(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 bucket_max_size = 2;
  void clear_bucket_max_size();
  static const int kBucketMaxSizeFieldNumber = 2;
  ::PROTOBUF_NAMESPACE_ID::int32 bucket_max_size() const;
  void set_bucket_max_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filter_dsl_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pass_dsl_;
  ::PROTOBUF_NAMESPACE_ID::int32 polling_interval_;
  ::PROTOBUF_NAMESPACE_ID::int32 bucket_max_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto;
};
// -------------------------------------------------------------------

class ShuffleParam_Plan :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.ShuffleParam.Plan) */ {
 public:
  ShuffleParam_Plan();
  virtual ~ShuffleParam_Plan();

  ShuffleParam_Plan(const ShuffleParam_Plan& from);
  ShuffleParam_Plan(ShuffleParam_Plan&& from) noexcept
    : ShuffleParam_Plan() {
    *this = ::std::move(from);
  }

  inline ShuffleParam_Plan& operator=(const ShuffleParam_Plan& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShuffleParam_Plan& operator=(ShuffleParam_Plan&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ShuffleParam_Plan& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ShuffleParam_Plan* internal_default_instance() {
    return reinterpret_cast<const ShuffleParam_Plan*>(
               &_ShuffleParam_Plan_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(ShuffleParam_Plan* other);
  friend void swap(ShuffleParam_Plan& a, ShuffleParam_Plan& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ShuffleParam_Plan* New() const final {
    return CreateMaybeMessage<ShuffleParam_Plan>(nullptr);
  }

  ShuffleParam_Plan* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ShuffleParam_Plan>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ShuffleParam_Plan& from);
  void MergeFrom(const ShuffleParam_Plan& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShuffleParam_Plan* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.ShuffleParam.Plan";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto);
    return ::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string plan_id = 1;
  void clear_plan_id();
  static const int kPlanIdFieldNumber = 1;
  const std::string& plan_id() const;
  void set_plan_id(const std::string& value);
  void set_plan_id(std::string&& value);
  void set_plan_id(const char* value);
  void set_plan_id(const char* value, size_t size);
  std::string* mutable_plan_id();
  std::string* release_plan_id();
  void set_allocated_plan_id(std::string* plan_id);

  // string tag_name = 5;
  void clear_tag_name();
  static const int kTagNameFieldNumber = 5;
  const std::string& tag_name() const;
  void set_tag_name(const std::string& value);
  void set_tag_name(std::string&& value);
  void set_tag_name(const char* value);
  void set_tag_name(const char* value, size_t size);
  std::string* mutable_tag_name();
  std::string* release_tag_name();
  void set_allocated_tag_name(std::string* tag_name);

  // .abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam bucket_polling_shuffle = 7;
  bool has_bucket_polling_shuffle() const;
  void clear_bucket_polling_shuffle();
  static const int kBucketPollingShuffleFieldNumber = 7;
  const ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam& bucket_polling_shuffle() const;
  ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* release_bucket_polling_shuffle();
  ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* mutable_bucket_polling_shuffle();
  void set_allocated_bucket_polling_shuffle(::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* bucket_polling_shuffle);

  // int32 input_num = 2;
  void clear_input_num();
  static const int kInputNumFieldNumber = 2;
  ::PROTOBUF_NAMESPACE_ID::int32 input_num() const;
  void set_input_num(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 output_num = 3;
  void clear_output_num();
  static const int kOutputNumFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int32 output_num() const;
  void set_output_num(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 shuffle_type = 4;
  void clear_shuffle_type();
  static const int kShuffleTypeFieldNumber = 4;
  ::PROTOBUF_NAMESPACE_ID::int32 shuffle_type() const;
  void set_shuffle_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 tag_id = 6;
  void clear_tag_id();
  static const int kTagIdFieldNumber = 6;
  ::PROTOBUF_NAMESPACE_ID::int32 tag_id() const;
  void set_tag_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.ShuffleParam.Plan)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr plan_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tag_name_;
  ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* bucket_polling_shuffle_;
  ::PROTOBUF_NAMESPACE_ID::int32 input_num_;
  ::PROTOBUF_NAMESPACE_ID::int32 output_num_;
  ::PROTOBUF_NAMESPACE_ID::int32 shuffle_type_;
  ::PROTOBUF_NAMESPACE_ID::int32 tag_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto;
};
// -------------------------------------------------------------------

class ShuffleParam :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.ShuffleParam) */ {
 public:
  ShuffleParam();
  virtual ~ShuffleParam();

  ShuffleParam(const ShuffleParam& from);
  ShuffleParam(ShuffleParam&& from) noexcept
    : ShuffleParam() {
    *this = ::std::move(from);
  }

  inline ShuffleParam& operator=(const ShuffleParam& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShuffleParam& operator=(ShuffleParam&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ShuffleParam& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ShuffleParam* internal_default_instance() {
    return reinterpret_cast<const ShuffleParam*>(
               &_ShuffleParam_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ShuffleParam* other);
  friend void swap(ShuffleParam& a, ShuffleParam& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ShuffleParam* New() const final {
    return CreateMaybeMessage<ShuffleParam>(nullptr);
  }

  ShuffleParam* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ShuffleParam>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ShuffleParam& from);
  void MergeFrom(const ShuffleParam& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShuffleParam* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.ShuffleParam";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto);
    return ::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ShuffleParam_BucketPollingShuffleParam BucketPollingShuffleParam;
  typedef ShuffleParam_Plan Plan;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.strategy.ShuffleParam.Plan plans = 1;
  int plans_size() const;
  void clear_plans();
  static const int kPlansFieldNumber = 1;
  ::abc::recommend_plt::strategy::ShuffleParam_Plan* mutable_plans(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam_Plan >*
      mutable_plans();
  const ::abc::recommend_plt::strategy::ShuffleParam_Plan& plans(int index) const;
  ::abc::recommend_plt::strategy::ShuffleParam_Plan* add_plans();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam_Plan >&
      plans() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.ShuffleParam)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam_Plan > plans_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto;
};
// -------------------------------------------------------------------

class BoostParam :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.BoostParam) */ {
 public:
  BoostParam();
  virtual ~BoostParam();

  BoostParam(const BoostParam& from);
  BoostParam(BoostParam&& from) noexcept
    : BoostParam() {
    *this = ::std::move(from);
  }

  inline BoostParam& operator=(const BoostParam& from) {
    CopyFrom(from);
    return *this;
  }
  inline BoostParam& operator=(BoostParam&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BoostParam& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BoostParam* internal_default_instance() {
    return reinterpret_cast<const BoostParam*>(
               &_BoostParam_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(BoostParam* other);
  friend void swap(BoostParam& a, BoostParam& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BoostParam* New() const final {
    return CreateMaybeMessage<BoostParam>(nullptr);
  }

  BoostParam* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BoostParam>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BoostParam& from);
  void MergeFrom(const BoostParam& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BoostParam* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.BoostParam";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto);
    return ::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.BoostParam)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto;
};
// -------------------------------------------------------------------

class StrategyParamOld :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.StrategyParamOld) */ {
 public:
  StrategyParamOld();
  virtual ~StrategyParamOld();

  StrategyParamOld(const StrategyParamOld& from);
  StrategyParamOld(StrategyParamOld&& from) noexcept
    : StrategyParamOld() {
    *this = ::std::move(from);
  }

  inline StrategyParamOld& operator=(const StrategyParamOld& from) {
    CopyFrom(from);
    return *this;
  }
  inline StrategyParamOld& operator=(StrategyParamOld&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StrategyParamOld& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StrategyParamOld* internal_default_instance() {
    return reinterpret_cast<const StrategyParamOld*>(
               &_StrategyParamOld_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(StrategyParamOld* other);
  friend void swap(StrategyParamOld& a, StrategyParamOld& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StrategyParamOld* New() const final {
    return CreateMaybeMessage<StrategyParamOld>(nullptr);
  }

  StrategyParamOld* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StrategyParamOld>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StrategyParamOld& from);
  void MergeFrom(const StrategyParamOld& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StrategyParamOld* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.StrategyParamOld";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto);
    return ::descriptor_table_proto_2frecommend_5fshuffle_5fparam_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.strategy.ShuffleParam shuffle_params = 1;
  int shuffle_params_size() const;
  void clear_shuffle_params();
  static const int kShuffleParamsFieldNumber = 1;
  ::abc::recommend_plt::strategy::ShuffleParam* mutable_shuffle_params(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam >*
      mutable_shuffle_params();
  const ::abc::recommend_plt::strategy::ShuffleParam& shuffle_params(int index) const;
  ::abc::recommend_plt::strategy::ShuffleParam* add_shuffle_params();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam >&
      shuffle_params() const;

  // repeated .abc.recommend_plt.strategy.BoostParam boost_params = 2;
  int boost_params_size() const;
  void clear_boost_params();
  static const int kBoostParamsFieldNumber = 2;
  ::abc::recommend_plt::strategy::BoostParam* mutable_boost_params(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::BoostParam >*
      mutable_boost_params();
  const ::abc::recommend_plt::strategy::BoostParam& boost_params(int index) const;
  ::abc::recommend_plt::strategy::BoostParam* add_boost_params();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::BoostParam >&
      boost_params() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.StrategyParamOld)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam > shuffle_params_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::BoostParam > boost_params_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fshuffle_5fparam_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ShuffleParam_BucketPollingShuffleParam

// int32 polling_interval = 1;
inline void ShuffleParam_BucketPollingShuffleParam::clear_polling_interval() {
  polling_interval_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_BucketPollingShuffleParam::polling_interval() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.polling_interval)
  return polling_interval_;
}
inline void ShuffleParam_BucketPollingShuffleParam::set_polling_interval(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  polling_interval_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.polling_interval)
}

// int32 bucket_max_size = 2;
inline void ShuffleParam_BucketPollingShuffleParam::clear_bucket_max_size() {
  bucket_max_size_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_BucketPollingShuffleParam::bucket_max_size() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.bucket_max_size)
  return bucket_max_size_;
}
inline void ShuffleParam_BucketPollingShuffleParam::set_bucket_max_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bucket_max_size_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.bucket_max_size)
}

// string filter_dsl = 3;
inline void ShuffleParam_BucketPollingShuffleParam::clear_filter_dsl() {
  filter_dsl_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ShuffleParam_BucketPollingShuffleParam::filter_dsl() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
  return filter_dsl_.GetNoArena();
}
inline void ShuffleParam_BucketPollingShuffleParam::set_filter_dsl(const std::string& value) {
  
  filter_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_filter_dsl(std::string&& value) {
  
  filter_dsl_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_filter_dsl(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  filter_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_filter_dsl(const char* value, size_t size) {
  
  filter_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
}
inline std::string* ShuffleParam_BucketPollingShuffleParam::mutable_filter_dsl() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
  return filter_dsl_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ShuffleParam_BucketPollingShuffleParam::release_filter_dsl() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
  
  return filter_dsl_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ShuffleParam_BucketPollingShuffleParam::set_allocated_filter_dsl(std::string* filter_dsl) {
  if (filter_dsl != nullptr) {
    
  } else {
    
  }
  filter_dsl_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), filter_dsl);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.filter_dsl)
}

// string pass_dsl = 4;
inline void ShuffleParam_BucketPollingShuffleParam::clear_pass_dsl() {
  pass_dsl_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ShuffleParam_BucketPollingShuffleParam::pass_dsl() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
  return pass_dsl_.GetNoArena();
}
inline void ShuffleParam_BucketPollingShuffleParam::set_pass_dsl(const std::string& value) {
  
  pass_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_pass_dsl(std::string&& value) {
  
  pass_dsl_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_pass_dsl(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  pass_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
}
inline void ShuffleParam_BucketPollingShuffleParam::set_pass_dsl(const char* value, size_t size) {
  
  pass_dsl_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
}
inline std::string* ShuffleParam_BucketPollingShuffleParam::mutable_pass_dsl() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
  return pass_dsl_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ShuffleParam_BucketPollingShuffleParam::release_pass_dsl() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
  
  return pass_dsl_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ShuffleParam_BucketPollingShuffleParam::set_allocated_pass_dsl(std::string* pass_dsl) {
  if (pass_dsl != nullptr) {
    
  } else {
    
  }
  pass_dsl_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pass_dsl);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam.pass_dsl)
}

// -------------------------------------------------------------------

// ShuffleParam_Plan

// string plan_id = 1;
inline void ShuffleParam_Plan::clear_plan_id() {
  plan_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ShuffleParam_Plan::plan_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
  return plan_id_.GetNoArena();
}
inline void ShuffleParam_Plan::set_plan_id(const std::string& value) {
  
  plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
}
inline void ShuffleParam_Plan::set_plan_id(std::string&& value) {
  
  plan_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
}
inline void ShuffleParam_Plan::set_plan_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
}
inline void ShuffleParam_Plan::set_plan_id(const char* value, size_t size) {
  
  plan_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
}
inline std::string* ShuffleParam_Plan::mutable_plan_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
  return plan_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ShuffleParam_Plan::release_plan_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
  
  return plan_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ShuffleParam_Plan::set_allocated_plan_id(std::string* plan_id) {
  if (plan_id != nullptr) {
    
  } else {
    
  }
  plan_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), plan_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.ShuffleParam.Plan.plan_id)
}

// int32 input_num = 2;
inline void ShuffleParam_Plan::clear_input_num() {
  input_num_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_Plan::input_num() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.input_num)
  return input_num_;
}
inline void ShuffleParam_Plan::set_input_num(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  input_num_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.input_num)
}

// int32 output_num = 3;
inline void ShuffleParam_Plan::clear_output_num() {
  output_num_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_Plan::output_num() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.output_num)
  return output_num_;
}
inline void ShuffleParam_Plan::set_output_num(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  output_num_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.output_num)
}

// int32 shuffle_type = 4;
inline void ShuffleParam_Plan::clear_shuffle_type() {
  shuffle_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_Plan::shuffle_type() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.shuffle_type)
  return shuffle_type_;
}
inline void ShuffleParam_Plan::set_shuffle_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  shuffle_type_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.shuffle_type)
}

// string tag_name = 5;
inline void ShuffleParam_Plan::clear_tag_name() {
  tag_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ShuffleParam_Plan::tag_name() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
  return tag_name_.GetNoArena();
}
inline void ShuffleParam_Plan::set_tag_name(const std::string& value) {
  
  tag_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
}
inline void ShuffleParam_Plan::set_tag_name(std::string&& value) {
  
  tag_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
}
inline void ShuffleParam_Plan::set_tag_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tag_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
}
inline void ShuffleParam_Plan::set_tag_name(const char* value, size_t size) {
  
  tag_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
}
inline std::string* ShuffleParam_Plan::mutable_tag_name() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
  return tag_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ShuffleParam_Plan::release_tag_name() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
  
  return tag_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ShuffleParam_Plan::set_allocated_tag_name(std::string* tag_name) {
  if (tag_name != nullptr) {
    
  } else {
    
  }
  tag_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tag_name);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_name)
}

// int32 tag_id = 6;
inline void ShuffleParam_Plan::clear_tag_id() {
  tag_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ShuffleParam_Plan::tag_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_id)
  return tag_id_;
}
inline void ShuffleParam_Plan::set_tag_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  tag_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.ShuffleParam.Plan.tag_id)
}

// .abc.recommend_plt.strategy.ShuffleParam.BucketPollingShuffleParam bucket_polling_shuffle = 7;
inline bool ShuffleParam_Plan::has_bucket_polling_shuffle() const {
  return this != internal_default_instance() && bucket_polling_shuffle_ != nullptr;
}
inline void ShuffleParam_Plan::clear_bucket_polling_shuffle() {
  if (GetArenaNoVirtual() == nullptr && bucket_polling_shuffle_ != nullptr) {
    delete bucket_polling_shuffle_;
  }
  bucket_polling_shuffle_ = nullptr;
}
inline const ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam& ShuffleParam_Plan::bucket_polling_shuffle() const {
  const ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* p = bucket_polling_shuffle_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.Plan.bucket_polling_shuffle)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam*>(
      &::abc::recommend_plt::strategy::_ShuffleParam_BucketPollingShuffleParam_default_instance_);
}
inline ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* ShuffleParam_Plan::release_bucket_polling_shuffle() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.ShuffleParam.Plan.bucket_polling_shuffle)
  
  ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* temp = bucket_polling_shuffle_;
  bucket_polling_shuffle_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* ShuffleParam_Plan::mutable_bucket_polling_shuffle() {
  
  if (bucket_polling_shuffle_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam>(GetArenaNoVirtual());
    bucket_polling_shuffle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.Plan.bucket_polling_shuffle)
  return bucket_polling_shuffle_;
}
inline void ShuffleParam_Plan::set_allocated_bucket_polling_shuffle(::abc::recommend_plt::strategy::ShuffleParam_BucketPollingShuffleParam* bucket_polling_shuffle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete bucket_polling_shuffle_;
  }
  if (bucket_polling_shuffle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      bucket_polling_shuffle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bucket_polling_shuffle, submessage_arena);
    }
    
  } else {
    
  }
  bucket_polling_shuffle_ = bucket_polling_shuffle;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.ShuffleParam.Plan.bucket_polling_shuffle)
}

// -------------------------------------------------------------------

// ShuffleParam

// repeated .abc.recommend_plt.strategy.ShuffleParam.Plan plans = 1;
inline int ShuffleParam::plans_size() const {
  return plans_.size();
}
inline void ShuffleParam::clear_plans() {
  plans_.Clear();
}
inline ::abc::recommend_plt::strategy::ShuffleParam_Plan* ShuffleParam::mutable_plans(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.ShuffleParam.plans)
  return plans_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam_Plan >*
ShuffleParam::mutable_plans() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.strategy.ShuffleParam.plans)
  return &plans_;
}
inline const ::abc::recommend_plt::strategy::ShuffleParam_Plan& ShuffleParam::plans(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.ShuffleParam.plans)
  return plans_.Get(index);
}
inline ::abc::recommend_plt::strategy::ShuffleParam_Plan* ShuffleParam::add_plans() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.strategy.ShuffleParam.plans)
  return plans_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam_Plan >&
ShuffleParam::plans() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.strategy.ShuffleParam.plans)
  return plans_;
}

// -------------------------------------------------------------------

// BoostParam

// -------------------------------------------------------------------

// StrategyParamOld

// repeated .abc.recommend_plt.strategy.ShuffleParam shuffle_params = 1;
inline int StrategyParamOld::shuffle_params_size() const {
  return shuffle_params_.size();
}
inline void StrategyParamOld::clear_shuffle_params() {
  shuffle_params_.Clear();
}
inline ::abc::recommend_plt::strategy::ShuffleParam* StrategyParamOld::mutable_shuffle_params(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.StrategyParamOld.shuffle_params)
  return shuffle_params_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam >*
StrategyParamOld::mutable_shuffle_params() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.strategy.StrategyParamOld.shuffle_params)
  return &shuffle_params_;
}
inline const ::abc::recommend_plt::strategy::ShuffleParam& StrategyParamOld::shuffle_params(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.StrategyParamOld.shuffle_params)
  return shuffle_params_.Get(index);
}
inline ::abc::recommend_plt::strategy::ShuffleParam* StrategyParamOld::add_shuffle_params() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.strategy.StrategyParamOld.shuffle_params)
  return shuffle_params_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::ShuffleParam >&
StrategyParamOld::shuffle_params() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.strategy.StrategyParamOld.shuffle_params)
  return shuffle_params_;
}

// repeated .abc.recommend_plt.strategy.BoostParam boost_params = 2;
inline int StrategyParamOld::boost_params_size() const {
  return boost_params_.size();
}
inline void StrategyParamOld::clear_boost_params() {
  boost_params_.Clear();
}
inline ::abc::recommend_plt::strategy::BoostParam* StrategyParamOld::mutable_boost_params(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.StrategyParamOld.boost_params)
  return boost_params_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::BoostParam >*
StrategyParamOld::mutable_boost_params() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.strategy.StrategyParamOld.boost_params)
  return &boost_params_;
}
inline const ::abc::recommend_plt::strategy::BoostParam& StrategyParamOld::boost_params(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.StrategyParamOld.boost_params)
  return boost_params_.Get(index);
}
inline ::abc::recommend_plt::strategy::BoostParam* StrategyParamOld::add_boost_params() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.strategy.StrategyParamOld.boost_params)
  return boost_params_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::BoostParam >&
StrategyParamOld::boost_params() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.strategy.StrategyParamOld.boost_params)
  return boost_params_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fshuffle_5fparam_2eproto
