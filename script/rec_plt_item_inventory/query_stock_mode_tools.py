#!/usr/bin/env python
# _*_ coding:utf-8 _*_


import os
import sys
import logging
import warnings
import threading
import time
import json
import random

import src.query_stock_mode_http as query_stock_mode_http

def main(site_uid, sku):
    query_stock_mode_obj = query_stock_mode_http.QueryStockModeHttp()
    post_dict = dict()
    post_dict["site_uid"] = site_uid
    post_dict["sku_arr"]=list()
    post_dict["sku_arr"].append(sku)

    print("---post_body:{0}".format(post_dict))
    rt, rsp_data = query_stock_mode_obj.post(post_dict, site_uid)
    print("---rt:{0} rsp_data:{1}".format(rt, rsp_data))

if __name__ == '__main__':
    site_uid = sys.argv[1]
    sku = int(sys.argv[2])

    main(site_uid, sku)