/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
        template <
            typename Try
            
            
        >
        struct try_catch<
            Try
            
            
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
               
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0
        >
        struct try_catch<
            Try
            ,
            A0
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1
        >
        struct try_catch<
            Try
            ,
            A0 , A1
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28
            >
        {};
     
    
    
    
    
    
    
    
        template <
            typename Try
            ,
            typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29
        >
        struct try_catch<
            Try
            ,
            A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29
        >
            : expr_ext<
                try_catch_actor
              , tag::try_catch
              , Try
              , A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29
            >
        {};
     
