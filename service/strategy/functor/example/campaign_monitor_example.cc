#include "service/strategy/functor/example/campaign_monitor_example.h"
#include "service/common/campaign_monitor_collector.h"
#include "util/logger.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

bool CampaignMonitorExample::Run(Accessor* accessor) {
    LOG_DEBUG << "CampaignMonitorExample run, strategy_id:" << strategy_id();
    
    // 示例：投放计划监控使用
    const std::string campaign_id = "campaign_001";  // 投放计划ID
    const std::string strategy_id = this->strategy_id();  // 当前策略ID
    
    // 1. 收集曝光指标
    accessor->AddCampaignMetric(campaign_id, strategy_id, "exposure", "count", 1);
    
    // 2. 收集点击指标 (假设有点击)
    if (ShouldCollectClick()) {
        accessor->AddCampaignMetric(campaign_id, strategy_id, "click", "count", 1);
    }
    
    // 3. 收集算子执行耗时
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 执行具体的算子逻辑
    bool result = ExecuteStrategy(accessor);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 收集耗时指标
    accessor->AddCampaignMetric(campaign_id, strategy_id, "latency", "duration", duration.count());
    
    // 4. 收集效果数据
    if (result) {
        accessor->AddCampaignEffect(campaign_id, 1, 0, 0);  // 1次曝光
    }
    
    // 5. 设置投放计划配置 (可选)
    campaign::CampaignConfig config;
    config.set_campaign_id(campaign_id);
    config.set_campaign_name("测试投放计划");
    config.add_strategy_ids(strategy_id);
    config.add_scene_ids(std::to_string(accessor->scene_id()));
    config.set_status(1);  // 启用状态
    
    SET_CAMPAIGN_CONFIG(campaign_id, config);
    
    LOG_INFO << "Campaign monitor example completed for campaign_id:" << campaign_id
             << ", strategy_id:" << strategy_id << ", result:" << result;
    
    return result;
}

bool CampaignMonitorExample::ShouldCollectClick() {
    // 模拟点击概率 (实际应用中根据业务逻辑判断)
    return (rand() % 100) < 5;  // 5% 点击率
}

bool CampaignMonitorExample::ExecuteStrategy(Accessor* accessor) {
    // 模拟策略执行逻辑
    const auto& items = accessor->OriginItems();
    if (items.empty()) {
        LOG_DEBUG << "No items to process";
        return false;
    }
    
    // 模拟一些处理逻辑
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    LOG_DEBUG << "Processed " << items.size() << " items";
    return true;
}

} // namespace strategy
} // namespace recommend_plt
} // namespace abc
