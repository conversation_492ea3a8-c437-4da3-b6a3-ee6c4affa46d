#include "service/strategy/functor/extractor/tf_model_item_fea_extractor.h"

#include "service/feature/feature.h"
#include "service/strategy/common/common_def.h"
#include "service/strategy/executor/strategy_pool.h"
#include "service/updater/api/data_auto_api.h"
#include "service/strategy/common/strategy_util.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

const std::string kRecPreRank = "rec_prerank";
const std::string kRecRank = "rec_rank";

bool TFModelItemFeaExtractor::Init() {
    if(!InitDslHelper(ItemsList::ARRANGED_ITEMS)) {
        LOG_ERROR << "TFModelItemFeaExtractor feature generater init error.";
        return false;
    }
    if (!is_async()) {
        updater::Service service;
        if (!service_comm::ServiceUtility::GetService(kServiceTfModel, &service)) {
            SPD_LOG_ERROR("get service failed, service_name: {}", kServiceTfModel);
            return false;
        }
        sync_helper_.reset(new SyncTfModelHelper(service.target(),
                                                 service.timeout_ms(),
                                                 service.gateway()));
    }
    updater::Service service;
    if (service_comm::ServiceUtility::GetService(kServiceTfModel, &service)) {
        tf_gateway_ = service.gateway();
    }
    if (service_comm::ServiceUtility::GetService(kServiceTfModelNanjing, &service)) {
        nanjing_gateway_ = service.gateway();
    }
    return true;
}

void TFModelItemFeaExtractor::Reset() {
    Extractor::Reset();
    tf_param_.Clear();
    tf_req_.clear();
    model_site_uid_map_.clear();
    fea_site_uid_map_.clear();
    output2fea_name_map_.clear();
    score_map_.clear();
    prefix_.clear();
    tf_cnt_.store(0, std::memory_order_relaxed);
    rank_score_num_ = 0;
    rank_batch_ = 0;
    rank_tfm_batch_ = 0;
    batch_item_num_ = 0;
    is_nanjing_rank_ = false;
    predict_type_ = mmp::PredictType_Rank;
    if (sync_helper_) {
        sync_helper_->Reset();
    }
    candidate_arranged_idx_.clear();
    arranged_items_.clear();
}

bool TFModelItemFeaExtractor::Bind(const std::string& strategy_id, const strategy::StrategyParam& param) {
    set_strategy_id(strategy_id);
    set_name(param.functor_name());
    tf_param_ = std::move(param.tf_model_item_fea_extractor_param());
    for (const auto& mo: tf_param_.model_output()) {
        SetOutputItemFeaName(mo.output_item_fea());
    }
    for(auto& ctx_fea : tf_param_.input_ctx_fea()) {
        SetRequiredCtxFeaName(ctx_fea);
    }
    for(auto& item_fea : tf_param_.input_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }
    for(auto& item_fea : tf_param_.trans_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }
    if (tf_param_.dsl_select().empty()) {
        dsl_helper()->set_need_dsl(false);
    }
    LOG_DEBUG << "tf_parm_:" << tf_param_.ShortDebugString();
    return true;
}

bool TFModelItemFeaExtractor::PackCtxRequest(Accessor* accessor, mmp::TFModelRequest* tf_req) {
    tf_req->set_model_id(tf_param_.model_id());

    std::string model_site_uid = accessor->site_uid();
    auto iter = model_site_uid_map_.find(accessor->site_uid());
    if (iter != model_site_uid_map_.end()) {
        model_site_uid = iter->second;
    }
    tf_req->set_model_site_uid(model_site_uid);

    if (IsModelDowngraded(tf_param_.model_id(), model_site_uid, accessor)) {
        return false;
    }

    std::string fea_site_uid = accessor->site_uid();
    iter = fea_site_uid_map_.find(accessor->site_uid());
    if (iter != fea_site_uid_map_.end()) {
        fea_site_uid = iter->second;
    }
    final_site_uid_ = fea_site_uid;
    tf_req->set_item_feature_site_uid(fea_site_uid);

    if (rank_score_num_ < 100) {
        rank_batch_ = 1;
    }
    tf_req->set_batch(rank_batch_);
    tf_req->set_item_source(accessor->item_source());

    tf_req->set_trace_id(accessor->trace_id());
    tf_req->set_scene_id(accessor->scene_id());
    if (accessor->IsDebugModeWhiteList(kServiceTfModel)) {
        tf_req->set_debug_mode(accessor->debug_mode());
    } else {
        tf_req->set_debug_mode(0);
    }
    tf_req->set_item_type(accessor->item_type());
    tf_req->set_caller("rec_rank");
    tf_req->set_predict_type(predict_type_);
    tf_req->set_poskey(accessor->poskey());

    // 多目标场景需指定模型目标名
    for (const auto& kv : output2fea_name_map_) {
        tf_req->add_output_names(kv.first);
    }

    if (tf_req->model_site_uid().empty() || tf_req->item_feature_site_uid().empty() || tf_req->model_id().empty()) {
        accessor->AddErrorReport(strategy_id(), "tf_req_empty", "tf_req_empty");
        return false;
    }

    // 根据用户信息和上下文 rcontext_info 生成 user 和 ctx 特征数据
    // context feature
    accessor->CtxFeatureToPbMap(tf_req->mutable_raw_feature());

    return true;
}

bool TFModelItemFeaExtractor::PackItemRequest(Accessor* accessor, const mmp::TFModelRequest& tf_req) {
    arranged_items_ = accessor->ArrangedItems(); // deep copy
    // 获取指定打分的商品
    if (!ExecuteDsl(accessor)) {
        return false;
    }
    rank_score_num_ = std::min({rank_score_num_,
                                static_cast<int64_t>(arranged_items_.size()),
                                static_cast<int64_t>(candidate_arranged_idx_.size())});
    std::vector<int64_t> items(rank_score_num_);
    candidate_arranged_idx_.resize(rank_score_num_);
    if (predict_type_ == mmp::PredictType_Position) {
        // 坑位价值分模型为坑位id
        std::iota(items.begin(), items.end(), 0);
    } else {
        for (size_t i = 0; i < rank_score_num_ && i < candidate_arranged_idx_.size(); ++i) {
            items[i] = arranged_items_[candidate_arranged_idx_[i]]->item_id;
        }
    }

    if (rank_score_num_ < 100 || rank_tfm_batch_ <= 0) {
        rank_tfm_batch_ = 1;
    }

    batch_item_num_ = rank_score_num_ % rank_tfm_batch_ == 0 ?
                      rank_score_num_ / rank_tfm_batch_ : rank_score_num_ / rank_tfm_batch_ + 1;

    // 需要透传的物料特征
    std::vector<feature::FeaturePtr> trans_item_fea_ptr(tf_param_.trans_item_fea_size(), nullptr);
    for (size_t i = 0; i < tf_param_.trans_item_fea_size(); ++i) {
        const std::string& trans_fea_name = tf_param_.trans_item_fea(i);
        const auto fea_ptr = accessor->GetArrangedItemFeaturePtr(trans_fea_name);
        if (!fea_ptr) {
            accessor->AddErrorReport(strategy_id(), "trans_item_fea is null, fea_name: " + trans_fea_name);
            return false;
        }
        trans_item_fea_ptr[i] = fea_ptr;
        accessor->AddItemLanding(trans_fea_name);
    }

    for (size_t i = 0; i < rank_tfm_batch_; ++i) {
        auto req_ptr = google::protobuf::Arena::CreateMessage<mmp::TFModelRequest>(accessor->Arena());
        req_ptr->CopyFrom(tf_req);
        tf_req_.emplace_back(req_ptr);
        auto& req = *tf_req_.back();
        int64_t begin_idx = i * batch_item_num_;
        int64_t end_idx = (i + 1) * batch_item_num_;
        end_idx = std::min(end_idx, rank_score_num_);

        req.set_batch_count(rank_tfm_batch_);
        req.set_batch_idx(i);
        for (size_t j = begin_idx; j < end_idx; ++j) {
            req.add_item_id(items[j]);
        }
        if (!TransItemFeaGenerate(trans_item_fea_ptr, &req, begin_idx, end_idx)) {
            return false;
        }
    }

    accessor->AddCtxMonitorV2(strategy_id(), "stat-tfm_batch_num", (int)rank_tfm_batch_);
    accessor->AddCtxMonitorV2(strategy_id(), util::Join("-",
                              "stat", Stage() == FunctorStage::RANK ? kRecpltFn : kRecpltPn,
                              std::to_string(rank_batch_),
                              std::to_string(rank_tfm_batch_)),
                              rank_score_num_);

    return true;
}

bool ParseSplitParam(const std::string& src, int64_t* dest1, int64_t* dest2 = nullptr, int64_t* dest3 = nullptr) {
    if (src.empty()) {
        return false;
    }
    std::vector<std::string> exp_num_params;
    boost::split(exp_num_params, src, boost::is_any_of("-"));
    if (exp_num_params.size() == 0) {
        return false;
    }
    *dest1 = atoi(exp_num_params[0].c_str());

    if (exp_num_params.size() >= 2 && dest2) {
        *dest2 = atoi(exp_num_params[1].c_str());
    }
    if (exp_num_params.size() == 3 && dest3) {
        *dest3 = atoi(exp_num_params[2].c_str());
    }
    return true;
}

void TFModelItemFeaExtractor::SetBatchParams(Accessor* accessor) {
    // 分包默认参数
    if (Stage() == FunctorStage::RANK) {
        rank_score_num_ = 400;
        rank_batch_ = 4;
        rank_tfm_batch_ = 1;
    } else if (Stage() == FunctorStage::PRERANK) {
        rank_score_num_ = 2000;
        rank_batch_ = 2;
        rank_tfm_batch_ = 4;
    }

    const static std::unordered_map<int64_t, std::string> stage2params_map = {
        {FunctorStage::RANK, kRecpltFn},
        {FunctorStage::PRERANK, kRecpltPn}
    };
    // 获取打分控制参数 (优先获取策略维度的参数，策略维度参数为空时获取请求维度的控制参数)
    auto params_iter = stage2params_map.find(Stage());
    if (params_iter == stage2params_map.end()) {
        return;
    }
    auto fea_ptr = accessor->GetCtxFeaturePtr(params_iter->second, strategy_id());
    const std::string& batch_params = feature::CastDefaultPadding<std::string>(fea_ptr, kEmptyString);
    ParseSplitParam(batch_params, &rank_score_num_, &rank_batch_, &rank_tfm_batch_);
}

void TFModelItemFeaExtractor::ParseParam(Accessor* accessor) {
    predict_type_ = static_cast<mmp::PredictType>(tf_param_.predict_type());
    if (predict_type_ == mmp::PredictType_Position) {
        rank_score_num_ = 1000;
        rank_batch_ = 1;
    } else {
        prefix_ = "rec_rank";
        is_nanjing_rank_ = accessor->GetCtxFeature<int64_t>("is_nanjing_rank") && Stage() == FunctorStage::RANK;
        if (Stage() == FunctorStage::PRERANK) {
            prefix_ = "rec_prerank";
        }
        SetBatchParams(accessor);
    }
    // 获取打分数量柔性降级参数, TODO: remove
    int64_t flex_score_num = 0;
    if (Stage() == FunctorStage::PRERANK || IsPrerankStage()) {
        flex_score_num = accessor->GetCtxFeature<int64_t>(kPreRankFlexNum);
    }
    if (Stage() == FunctorStage::RANK || IsRankStage()) {
        flex_score_num = accessor->GetCtxFeature<int64_t>(kRankFlexNum);
    }
    if (flex_score_num > 0) {
        rank_score_num_ = std::min(flex_score_num, rank_score_num_);
    }
    set_effect_num(rank_score_num_);
    // 统一控制打分数量
    rank_score_num_ = GetControlParam<int64_t>(accessor, kAutoDegradeEffectNum, rank_score_num_);
    rank_score_num_ = GetControlParam<int64_t>(accessor, kEffectNum, rank_score_num_);

    auto fea_ptr = accessor->GetArrangedItemFeaturePtr(kFeaGFilter);
    if (fea_ptr && fea_ptr->type() == fmp::LIST_INT64) {
        auto fdata = feature::CastFeature<fmp::LIST_INT64>(fea_ptr);
        auto pos = std::lower_bound(fdata->data().begin(), fdata->data().end(), 1) - fdata->data().begin();
        if (pos < rank_score_num_) {
            rank_score_num_ = pos;
        }
    }

    LOG_DEBUG << "prefix:" << prefix_ << ", rank_score_num: " << rank_score_num_
        << ", rank_batch: " << rank_batch_ << ", rank_tfm_batch: "
        << rank_tfm_batch_ << ", is_nanjing_rank:" << is_nanjing_rank_;

    for (const auto& kv : tf_param_.model_site_uid_mapping()) {
        for (const auto& site_uid : kv.raw_site_uid()) {
            model_site_uid_map_[site_uid] = kv.map_site_uid();
        }
    }
    for (const auto& kv : tf_param_.item_site_uid_mapping()) {
        for (const auto& site_uid : kv.raw_site_uid()) {
            fea_site_uid_map_[site_uid] = kv.map_site_uid();
        }
    }

    for (const auto& mo : tf_param_.model_output()) {
        if (mo.model_output().empty()) {
            continue;
        }
        output2fea_name_map_[mo.model_output()] = mo.output_item_fea();
        score_map_[mo.output_item_fea()] = std::vector<float>(accessor->ArrangedItems().size(), 0.0f);
    }
}

bool TFModelItemFeaExtractor::PackRequest(Accessor* accessor) {
    if (rank_score_num_ <= 0) {
        LOG_DEBUG << "rank_score_num=0, model_id: " << tf_param_.model_id() << ", trace_id: " << accessor->trace_id()
                  << ", scene_id: " << accessor->scene_id();
        return false;
    }

    // 粗排降级(参与粗排数量小于精排打分数量)
    if (IsPrerankScoreNumDowngraded(accessor) || IsUserClickDowngraded(accessor)) {
        return false;
    }

    mmp::TFModelRequest* tf_req = google::protobuf::Arena::CreateMessage<mmp::TFModelRequest>(accessor->Arena());
    if (!PackCtxRequest(accessor, tf_req)) {
        return false;
    }

    if (!PackItemRequest(accessor, *tf_req)) {
        return false;
    }

    return true;
}

bool TFModelItemFeaExtractor::TransItemFeaGenerate(const std::vector<feature::FeaturePtr>& trans_item_fea_ptr,
                                                   mmp::TFModelRequest* tf_req, int begin_index, int end_index) {
    if (trans_item_fea_ptr.size() == 0) {
        return true;
    }
    if (tf_param_.trans_item_fea_size() != trans_item_fea_ptr.size()) {
        return false;
    }

    for (size_t i = 0; i < trans_item_fea_ptr.size(); ++i) {
        const auto fea_ptr = trans_item_fea_ptr[i];
        const auto& fea_name = tf_param_.trans_item_fea(i);
        if (!fea_ptr) {
            continue;
        }
        fmp::Feature feature;
        if (fea_ptr->type() == fmp::LIST_FLOAT32) {
            auto fdata = static_cast<const feature::FeatureVector<float>*>(fea_ptr.get());
            for (size_t j = begin_index; j < end_index; ++j) {
                feature.mutable_float_list()->add_value(fdata->data(candidate_arranged_idx_[j]));
            }
        } else if (fea_ptr->type() == fmp::LIST_INT64) {
            auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(fea_ptr.get());
            for (size_t j = begin_index; j < end_index; ++j) {
                feature.mutable_int64_list()->add_value(fdata->data(candidate_arranged_idx_[j]));
            }
        } else {
            LOG_DEBUG << "unsupported trans item feature type, fea_name: " << fea_name
                      << ", fea_type: " << fea_ptr->type();
            continue;
        }
        (*tf_req->mutable_raw_feature())[fea_name] = std::move(feature);
    }

    return true;
}

bool TFModelItemFeaExtractor::ExecuteDsl(Accessor* accessor) {
    size_t item_num = accessor->ArrangedItems().size();
    if (!dsl_helper()->need_dsl()) {
        candidate_arranged_idx_.resize(item_num);
        std::iota(candidate_arranged_idx_.begin(), candidate_arranged_idx_.end(), 0);
        return true;
    }

    if (dsl_helper()->dsl_name_size() <= 0) {
        accessor->AddErrorReport(strategy_id(), "dsl_name empty", "dsl_name_empty");
        return false;
    }
    auto res = dsl_helper()->GetFeature(dsl_helper()->dsl_name(0));
    if (!res) {
        return false;
    }

    candidate_arranged_idx_.reserve(item_num);
    switch (res->type()) {
        case fmp::LIST_BOOL: {
            auto fdata = feature::CastDefaultPadding<fmp::LIST_BOOL>(res);
            for (size_t i = 0; i < fdata.size() && i < rank_score_num_; ++i) {
                if (fdata[i]) { candidate_arranged_idx_.push_back(i); }
            }
            break;
        }
        case fmp::LIST_INT64: {
            auto fdata = feature::CastDefaultPadding<fmp::LIST_INT64>(res);
            for (size_t i = 0; i < fdata.size() && i < rank_score_num_; ++i) {
                if (fdata[i] > 0) { candidate_arranged_idx_.push_back(i); }
            }
            break;
        }
        default:
            accessor->AddErrorReport(strategy_id(), "not support feature type: " + std::to_string(res->type()));
            return false;
    }
    accessor->AddItemExplain(strategy_id(), "dsl_select", res);
    if (candidate_arranged_idx_.size() <= 0) {
        return false;
    }
    return true;
}

bool TFModelItemFeaExtractor::PrepareRun(Accessor* accessor) {
    ParseParam(accessor);
    return true;
}

bool TFModelItemFeaExtractor::Run(Accessor* accessor) {
    accessor->AddStageExpect(Stage());
    if (accessor->OriginItems().empty()) {
        DoCallback();
        return true;
    }
    ParseParam(accessor);
    if (!PackRequest(accessor)) {
        DoCallback();
        return false;
    }

    bool ret = false;
#ifndef UNIT_TEST
    if (is_async()) {
        ret = AsyncTfModel(accessor);
    } else {
        ret = SyncTfModel(accessor);
    }
#endif
    if (!ret) {
        DoCallback();
    }
    return ret;
}

bool TFModelItemFeaExtractor::AsyncTfModel(Accessor* accessor) {
    // 异步访问tf_model
    const std::string& service_name = is_nanjing_rank_ ? kServiceTfModelNanjing : kServiceTfModel;
    const std::string& gateway = is_nanjing_rank_ ? nanjing_gateway_ : tf_gateway_;

    const auto timeout_ms = GetServiceTimeout(service_name, 80);
    int64_t timeout = GetTfmodelTimeout(timeout_ms, accessor);
    timeout = GetControlParam<int64_t>(accessor, kServiceTfTimeout, timeout);

    accessor->AddCtxMonitorV2(strategy_id(), "qps-" + util::Join("-", tf_param_.model_id(), final_site_uid_), 1);

    for (size_t i = 0 ; i < rank_tfm_batch_; ++i) {
        if (!StrategyAsyncPool::get_mutable_instance().AsyncPool()->Invoke<mmp::TFModelService>(
            &mmp::TFModelService::Stub::PrepareAsyncPredict,
            *tf_req_[i],
            gateway,
            timeout,
            [accessor, i, this](const grpc::Status& status, const int cost, mmp::TFModelResponse* rsp) {
                this->ProcessTfModelRsp(status, i, *rsp, accessor);
                tf_cnt_.fetch_add(1, std::memory_order_relaxed);
                size_t cnt = rank_tfm_batch_;
                if (tf_cnt_.compare_exchange_strong(cnt, rank_tfm_batch_ + 1,
                            std::memory_order_acq_rel)) {
                    this->SetScores(accessor);
                }
            }, accessor->trace_key())) {
                LOG_ERROR << "invoke service fail, strategy_id:" << strategy_id();
            //return false;
        }
    }

    return true;
}

bool TFModelItemFeaExtractor::SyncTfModel(Accessor* accessor) {
    // 同步访问tf_model
    if (!sync_helper_) {
        LOG_ERROR << "sync_helper_ is nullptr";
        return false;
    }

    const auto timeout_ms = GetServiceTimeout(kServiceTfModel);
    int64_t timeout = GetControlParam<int64_t>(accessor, kServiceTfTimeout, timeout_ms);
    sync_helper_->SetTimeout(std::max(timeout, int64_t(20)));

    for (size_t i = 0; i < tf_req_.size(); ++i) {
        sync_helper_->SendRequest(*tf_req_[i]);
    }
    sync_helper_->WaitResponses();
    auto& sub_results = sync_helper_->MutableResult();
    for (size_t i = 0; i < sub_results.size(); ++i) {
        ProcessTfModelRsp(sub_results[i]->status, i, sub_results[i]->rsp, accessor);
    }
    SetScores(accessor);

    return true;
}

bool TFModelItemFeaExtractor::CheckTfModelRsp(const grpc::Status& status, int index,
                                              const mmp::TFModelResponse& rsp,
                                              Accessor* accessor) {
    const std::string& serv_name = is_nanjing_rank_ ? kServiceTfModelNanjing : kServiceTfModel;
    // 分包：任意请求失败，上报策略为失败
    if (!status.ok()) {
        std::string error_msg = "tf_model grpc error, err_code: " + std::to_string(status.error_code()) +
                                ", service_name: " + serv_name + " " + util::Join("-", tf_param_.model_id(), final_site_uid_);
        accessor->AddErrorReport(strategy_id(), error_msg, "grpc-" + std::to_string(status.error_code()) + "-" + tf_param_.model_id());
        return false;
    }
    if (rsp.error_code() != mmp::SUCCESS) {
        std::string error_msg =  "tf_model response error, err_code: " + std::to_string(rsp.error_code()) +
                                ", service_name: " + serv_name + " " + util::Join("-", tf_param_.model_id(), final_site_uid_);
        accessor->AddErrorReport(strategy_id(), error_msg, "rsp-" + std::to_string(rsp.error_code()) + "-" + tf_param_.model_id());
        return false;
    }
    return true;
}

void TFModelItemFeaExtractor::FillUnmappedScores(int index,
                                                 const std::string& ori_obj_name,
                                                 const google::protobuf::RepeatedField<float>& scores,
                                                 Accessor* accessor) {
    boost::unique_lock<boost::shared_mutex> write_lock(rsp_mutex_);
    bool is_inl_strategy = accessor->IsInlStrategyID(strategy_id());
    std::string obj_name = is_inl_strategy ? ori_obj_name + kInLSuffix : ori_obj_name;
    auto score_map_iter = score_map_.find(obj_name);
    if (score_map_iter == score_map_.end()) {
        if (is_inl_strategy) {
            accessor->AddInlReplaceName(ori_obj_name);
        }
        score_map_.insert(std::make_pair(obj_name, std::vector<float>(accessor->ArrangedItems().size(), 0.0f)));
    }
    FillScores(index, obj_name, scores);
}

void TFModelItemFeaExtractor::FillMappedScores(int index,
                                               const std::string& mapped_obj_name,
                                               const google::protobuf::RepeatedField<float>& scores,
                                               Accessor* accessor) {
    boost::unique_lock<boost::shared_mutex> write_lock(rsp_mutex_);
    auto score_map_iter = score_map_.find(mapped_obj_name);
    if (score_map_iter == score_map_.end()) {
        score_map_.insert(std::make_pair(mapped_obj_name, std::vector<float>(accessor->ArrangedItems().size(), 0.0f)));
    }
    FillScores(index, mapped_obj_name, scores);
}

bool TFModelItemFeaExtractor::ProcessTfModelRsp(const grpc::Status& status, int index,
                                                const mmp::TFModelResponse& rsp,
                                                Accessor* accessor) {
    if (!CheckTfModelRsp(status, index, rsp, accessor)) {
        return false;
    }
    LOG_DEBUG << "tf model response output size:" << rsp.object_outputs_size()
              << ", rsp: " << rsp.ShortDebugString();

    int64_t score_num = tf_req_[index]->item_id_size();
    // 解析tf_model响应中的模型目标分
    for (size_t obj_idx = 0; obj_idx < rsp.object_outputs_size(); ++obj_idx) {
        const auto& obj = rsp.object_outputs(obj_idx);
        if (obj.score_size() != score_num) {
            accessor->AddErrorReport(strategy_id(), "check score size error, score_size=" + std::to_string(obj.score_size()) +
                                      ", item_size=" + std::to_string(score_num) + ", obj_name=" + obj.name(), "score_size_error-"+ tf_param_.model_id());
            continue;
        }

        auto output2fea_name_map_iter = output2fea_name_map_.find(obj.name());
        if (output2fea_name_map_iter == output2fea_name_map_.end()) {
            // 支持透传模型目标分(未指定)
            FillUnmappedScores(index, obj.name(), obj.score(), accessor);
        } else {
            FillMappedScores(index, output2fea_name_map_iter->second, obj.score(), accessor);
        }

        LOG_DEBUG << "object name:" << obj.name() << " score_size:" << obj.score_size();
    }

    // 兼容解析单目标模型打分
    if (rsp.score_size() > 0) {
        if (rsp.score_size() != score_num) {
            accessor->AddErrorReport(strategy_id(), "check score size error, score_size=" + std::to_string(rsp.score_size()) +
                                     ", item_size=" + std::to_string(score_num), "score_size_error-"+ tf_param_.model_id());
        }

        if (tf_param_.model_output_size() == 0) {
            LOG_ERROR << "tf_param error";
            return false;
        }
        FillMappedScores(index, tf_param_.model_output(0).output_item_fea(), rsp.score(), accessor);
    }
    return true;
}

bool TFModelItemFeaExtractor::SetScores(Accessor* accessor) {
    LOG_DEBUG << "SetScores in";
    bool all_zero_flag = true;
    std::unordered_map<std::string, std::vector<std::string>> model_output_map;
    for (const auto& kv : score_map_) {
        const std::string& fea_name = kv.first;
        auto fea_ptr = feature::FeatureFactory::CreateVector<float>(fea_name, kv.second);
        for (const auto& score : kv.second) { // 判断是否打分全为零，用于上报
            if (!all_zero_flag) {
                break;
            }
            if (score > 0) {
                all_zero_flag = false;
            }
        }
        accessor->AddItemFeature(fea_name, fea_ptr, arranged_items_);
        if (accessor->svr_mark_mode()) {
            accessor->AddItemSvrMarkV2(strategy_id(), tf_param_.model_id() + "_" + fea_name, accessor->GetOriginItemFeaturePtr(fea_name));
        }
        if (predict_type_ == mmp::PredictType_Position) {
            accessor->AddCtxFeature(fea_name, fea_ptr);
        }
        accessor->SetOutputModelMap(fea_name, tf_param_.model_id());
        model_output_map[tf_param_.model_id()].emplace_back(fea_name);

        LOG_DEBUG << "[tf_scores] " << fea_ptr->ToString();
        // 精排模型目标分覆盖率监控上报
        const auto& score_vec = kv.second;
        int64_t score_cnt = 0;
        std::for_each(score_vec.begin(), score_vec.end(), [&](float cur_score) {
            if (cur_score > 0.0f) { ++score_cnt; }
        });
        float cover_rate = rank_score_num_ == 0 ? 0.0f : (score_cnt * 1000.0f / rank_score_num_);
        accessor->AddCtxMonitorV2(strategy_id(), "hit-" + fea_name, static_cast<int>(cover_rate));
    }
    if (StrategyUtil::IsRankStage(strategy_id())) {
        accessor->SetRankOutputFeas(std::move(model_output_map));
        accessor->SetRankModelSite(tf_param_.model_id(), final_site_uid_);
    } else if (StrategyUtil::IsPrerankStage(strategy_id())) {
        accessor->SetPrerankOutputFeas(std::move(model_output_map));
        accessor->SetPrerankModelSite(tf_param_.model_id(), final_site_uid_);
    }

    if (accessor->debug_mode()) {
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_score_num");
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_batch", std::to_string(rank_batch_));
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_tfm_batch", std::to_string(rank_tfm_batch_));
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_trace_id", accessor->trace_id());
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_debug_mode", std::to_string(accessor->debug_mode()));
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_item_type", std::to_string(accessor->item_type()));
        accessor->AddCtxExplain(strategy_id(), prefix_ + "_poskey", accessor->poskey());
        for(const auto& kv : score_map_) {
            accessor->AddItemExplain(strategy_id(), kv.first);
        }
        for (const auto& trans_item_fea : tf_param_.trans_item_fea()) {
            accessor->AddItemExplain(strategy_id(), trans_item_fea);
        }
    }
    if (all_zero_flag) {
        accessor->AddErrorReport(strategy_id(), "score all zero-"+tf_param_.model_id(), "item_score_all_zero-"+tf_param_.model_id());
    } else {
        accessor->AddStageSuccess(Stage());
    }
    accessor->AddCtxFeature(prefix_ + "_score_num", (int)rank_score_num_);
    DoCallback();
    return true;
}

bool TFModelItemFeaExtractor::IsModelDowngraded(const std::string& model_id,
                                                const std::string& site_uid,
                                                Accessor* accessor) {
    std::string rank_model_downgrade_param = accessor->GetCtxFeature<std::string>("rank_model_downgrade_param");
    if (rank_model_downgrade_param.empty()) {
        return false;
    }

    std::vector<std::string> downgrade_params;
    boost::split(downgrade_params, rank_model_downgrade_param, boost::is_any_of(","));
    for (size_t i = 0; i < downgrade_params.size(); ++i) {
        std::vector<std::string> scene_model_site;
        boost::split(scene_model_site, downgrade_params[i], boost::is_any_of("-"));
        if (scene_model_site.size() < 4) {
            continue;
        }
        const std::string& downgrade_item_src = scene_model_site[0];
        const std::string& downgrade_scene_id = scene_model_site[1];
        const std::string& downgrade_model_id = scene_model_site[2];
        const std::string& downgrade_site_uid = scene_model_site[3];
        if (!downgrade_item_src.empty() && downgrade_item_src != std::to_string(accessor->item_source())) {
            continue;
        }
        if (!downgrade_scene_id.empty() && downgrade_scene_id != std::to_string(accessor->scene_id())) {
            continue;
        }
        if (!downgrade_model_id.empty() && downgrade_model_id != model_id) {
            continue;
        }
        if (!downgrade_site_uid.empty() && downgrade_site_uid != site_uid) {
            continue;
        }
        accessor->AddErrorReport(strategy_id(), "[NOT ERROR] tfmodel downgrade triggered, param: " + downgrade_params[i]);
        return true;
    }

    return false;
}

bool TFModelItemFeaExtractor::IsPrerankScoreNumDowngraded(Accessor* accessor) {
    if (Stage() != FunctorStage::PRERANK && !IsPrerankStage()) {
        return false;
    }
    // 场景级配置粗排降级开关
    auto fea_ptr = accessor->GetCtxFeaturePtr(kPrerankScoreNumDowngrade, strategy_id());
    int64_t is_downgrade = feature::CastDefaultPadding<int64_t>(fea_ptr);
    if (is_downgrade == 0) {
        return false;
    }

    // 获取精排打分数量
    int64_t rank_score_num = 400;
    ParseSplitParam(accessor->GetCtxFeature<std::string>(kRecpltFn), &rank_score_num);

    // 获取精排打分数量柔性降级参数, TODO: remove flex_num
    int64_t flex_score_num = accessor->GetCtxFeature<int64_t>(kRankFlexNum, rank_score_num);
    flex_score_num = GetControlParam<int64_t>(accessor, kEffectNum, flex_score_num);
    rank_score_num = std::min(flex_score_num, rank_score_num);

    // 进粗排数量小于精排打分数量时，降级粗排
    if (rank_score_num_ <= rank_score_num) {
        if (accessor->debug_mode()) {
            accessor->AddErrorReport(strategy_id(), "prerank tfmodel downgrade triggered, prerank_score_num=" +
                                     std::to_string(rank_score_num_) + ", less than rank_score_num=" + std::to_string(rank_score_num),
                                     "prerank_tf_num_downgrade");
        }
        return true;
    }

    return false;
}

bool TFModelItemFeaExtractor::IsUserTagDowngraded(Accessor* accessor) {
    std::string user_tag_downgrade = "";
    std::string stage_name = "";
    if (Stage() == FunctorStage::RANK || IsRankStage()) {
        user_tag_downgrade = accessor->GetCtxFeature<std::string>(kRankUserTagDowngrade);
        stage_name = "rank";
    } else if (Stage() == FunctorStage::PRERANK || IsPrerankStage()) {
        user_tag_downgrade = accessor->GetCtxFeature<std::string>(kPrerankUserTagDowngrade);
        stage_name = "prerank";
    }
    if (user_tag_downgrade.empty()) {
        return false;
    }

    bool is_downgrade = false;
    std::vector<std::string> downgrade_params;
    boost::split(downgrade_params, user_tag_downgrade, boost::is_any_of(","));
    for (size_t i = 0; i < downgrade_params.size(); ++i) {
        std::vector<std::string> user_tag_num;
        boost::split(user_tag_num, downgrade_params[i], boost::is_any_of("-"));
        if (user_tag_num.size() < 2) {
            continue;
        }
        std::string user_tag_name = "user_" + user_tag_num[0];
        int64_t limit_num = std::atol(user_tag_num[1].c_str());
        auto fea_ptr = accessor->GetCtxFeaturePtr(user_tag_name, strategy_id());
        if (!fea_ptr || fea_ptr->type() != fmp::LIST_INT64) {
            is_downgrade = true;
        } else {
            auto fea_typed = feature::CastFeature<fmp::LIST_INT64>(fea_ptr);
            if (!fea_typed || fea_typed->Size() < limit_num) {
                is_downgrade = true;
            }
        }
        if (is_downgrade) {
            accessor->AddErrorReport(strategy_id(), "[NOT ERROR] tfmodel user tag downgrade triggered, param=" + downgrade_params[i]);
            accessor->AddCtxMonitorV2(strategy_id(), stage_name + "_tf_user_" + user_tag_name + "_downgrade");
            break;
        }
    }

    return is_downgrade;
}

bool TFModelItemFeaExtractor::IsUserClickDowngraded(Accessor* accessor) {
    int64_t user_click_num = 0;
    std::string stage_name = "";
    if (Stage() == FunctorStage::RANK || IsRankStage()) {
        user_click_num = accessor->GetCtxFeature<int64_t>(kRankUserClickDowngrade);
        stage_name = "rank";
    } else if (Stage() == FunctorStage::PRERANK || IsPrerankStage()) {
        user_click_num = accessor->GetCtxFeature<int64_t>(kPrerankUserClickDowngrade);
        stage_name = "prerank";
    }
    if (user_click_num <= 0) {
        return false;
    }

    bool is_downgrade = false;
    auto fea_ptr = accessor->GetCtxFeaturePtr("user_2001");
    if (!fea_ptr || fea_ptr->type() != fmp::LIST_INT64) {
        is_downgrade = true;
    } else {
        auto fea_typed = feature::CastFeature<fmp::LIST_INT64>(fea_ptr);
        if (!fea_typed || fea_typed->Size() < user_click_num) {
            is_downgrade = true;
        }
    }

    if (is_downgrade) {
        accessor->AddErrorReport(strategy_id(), "tfmodel user_2001 downgrade triggered, expect_size=" + std::to_string(user_click_num),
                                 "tf_user_2001_downgrade");
    }

    return is_downgrade;
}

int64_t TFModelItemFeaExtractor::GetTfmodelTimeout(int64_t default_timeout, Accessor* accessor) {
    // 默认Apollo超时配置
    int64_t timeout = default_timeout > 30 ? default_timeout : 30;

    // 获取场景级超时配置
    int64_t timeout_scene_config = 0;
    if (Stage() == FunctorStage::RANK || IsRankStage()) {
        auto fea_ptr = accessor->GetCtxFeaturePtr(kRankTfModelTimeout, strategy_id());
        timeout_scene_config = feature::CastDefaultPadding<int64_t>(fea_ptr);
    } else if (Stage() == FunctorStage::PRERANK || IsPrerankStage()) {
        auto fea_ptr = accessor->GetCtxFeaturePtr(kPrerankTfModelTimeout, strategy_id());
        timeout_scene_config = feature::CastDefaultPadding<int64_t>(fea_ptr);
    }
       int64_t tmp = timeout_scene_config > 0 ? timeout_scene_config : timeout;

    return timeout_scene_config > 0 ? timeout_scene_config : timeout;
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
