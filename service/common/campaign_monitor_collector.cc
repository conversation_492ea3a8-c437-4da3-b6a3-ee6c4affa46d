#include "service/common/campaign_monitor_collector.h"
#include "service/common/kafka_reporter.h"
#include "util/logger.h"
#include "util/func.h"
#include <chrono>
#include <sstream>

namespace abc {
namespace recommend_plt {
namespace service_comm {

CampaignMonitorCollector::CampaignMonitorCollector() {
    // 初始化
}

void CampaignMonitorCollector::CollectCampaignMetric(
    const std::string& campaign_id,
    const std::string& strategy_id,
    const std::string& scene_id,
    int32_t item_source,
    const std::string& metric_name,
    const std::string& metric_type,
    int64_t metric_value,
    int64_t count) {
    
    if (!IsEnabled() || campaign_id.empty()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    const auto key = GenerateMetricKey(campaign_id, strategy_id, scene_id, item_source, metric_name);
    auto& metric = metric_cache_[key];
    
    // 设置基础信息
    metric.set_campaign_id(campaign_id);
    metric.set_strategy_id(strategy_id);
    metric.set_scene_id(scene_id);
    metric.set_item_source(item_source);
    metric.set_metric_name(metric_name);
    metric.set_metric_type(metric_type);
    
    // 聚合数据
    metric.set_metric_value(metric.metric_value() + metric_value);
    metric.set_count(metric.count() + count);
    metric.set_timestamp(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
    
    LOG_DEBUG << "Collected campaign metric: " << key 
              << ", value: " << metric_value << ", count: " << count;
}

void CampaignMonitorCollector::CollectCampaignEffect(
    const std::string& campaign_id,
    int64_t exposure,
    int64_t click,
    int64_t conversion) {
    
    if (!IsEnabled() || campaign_id.empty()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    auto& effect = effect_cache_[campaign_id];
    effect.exposure += exposure;
    effect.click += click;
    effect.conversion += conversion;
    effect.last_update = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    LOG_DEBUG << "Collected campaign effect: " << campaign_id 
              << ", exposure: " << exposure << ", click: " << click 
              << ", conversion: " << conversion;
}

bool CampaignMonitorCollector::ReportToKafka() {
    const auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 检查上报间隔
    const auto last_time = last_report_time_.load(std::memory_order_acquire);
    if (now - last_time < REPORT_INTERVAL_MS) {
        return true;
    }
    
    if (!last_report_time_.compare_exchange_strong(
        const_cast<int64_t&>(last_time), now, std::memory_order_acq_rel)) {
        return true; // 其他线程正在上报
    }
    
    campaign::CampaignMonitorReport report;
    
    // 复制并清空缓存数据
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        
        // 复制监控指标
        for (const auto& kv : metric_cache_) {
            auto* metric = report.add_campaign_metrics();
            *metric = kv.second;
        }
        
        // 复制效果数据并计算汇总指标
        for (const auto& kv : effect_cache_) {
            const auto& campaign_id = kv.first;
            const auto& effect = kv.second;
            
            auto* summary = report.add_campaign_summaries();
            summary->set_campaign_id(campaign_id);
            summary->set_total_exposure(effect.exposure);
            summary->set_total_click(effect.click);
            summary->set_total_conversion(effect.conversion);
            
            // 计算比率
            if (effect.exposure > 0) {
                summary->set_click_rate(static_cast<double>(effect.click) / effect.exposure);
            }
            if (effect.click > 0) {
                summary->set_conversion_rate(static_cast<double>(effect.conversion) / effect.click);
            }
            
            summary->set_summary_time(now);
        }
        
        // 清空缓存
        metric_cache_.clear();
        effect_cache_.clear();
    }
    
    // 上报到Kafka
    if (report.campaign_metrics_size() > 0 || report.campaign_summaries_size() > 0) {
        KAFKA_REPORTER_RPUSH("kafka_campaign_monitor",
                             service_comm::CampaignMonitorKafkaReporter,
                             std::move(report));
        
        LOG_INFO << "Reported campaign monitor data: metrics=" << report.campaign_metrics_size()
                 << ", summaries=" << report.campaign_summaries_size();
    }
    
    return true;
}

void CampaignMonitorCollector::SetCampaignConfig(
    const std::string& campaign_id, 
    const campaign::CampaignConfig& config) {
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    campaign_configs_[campaign_id] = config;
    
    LOG_INFO << "Set campaign config: " << campaign_id 
             << ", name: " << config.campaign_name()
             << ", strategies: " << config.strategy_ids_size();
}

const campaign::CampaignConfig* CampaignMonitorCollector::GetCampaignConfig(
    const std::string& campaign_id) const {
    
    std::lock_guard<std::mutex> lock(config_mutex_);
    auto it = campaign_configs_.find(campaign_id);
    return (it != campaign_configs_.end()) ? &it->second : nullptr;
}

std::string CampaignMonitorCollector::GenerateMetricKey(
    const std::string& campaign_id,
    const std::string& strategy_id,
    const std::string& scene_id,
    int32_t item_source,
    const std::string& metric_name) const {
    
    std::ostringstream oss;
    oss << campaign_id << "|" << strategy_id << "|" << scene_id 
        << "|" << item_source << "|" << metric_name;
    return oss.str();
}

} // namespace service_comm
} // namespace recommend_plt
} // namespace abc
